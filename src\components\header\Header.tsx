"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Menu, Grid3X3 } from "lucide-react";

import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import JsConsultants from "../intro/JsConsultants";
import DecryptedText from "../ui/DecryptedText";
import DecryptedShinyText from "../ui/DecryptedShinyText";
import ShinyText from "../ui/ShinyText";

const services = [
  {
    title: "Electrical",
    description: "Custom web applications and websites",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/web-development",
  },
  {
    title: "HVAC",
    description: "iOS and Android mobile applications",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/mobile-development",
  },
  {
    title: "Plumbing",
    description: "User interface and experience design",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/ui-ux-design",
  },
  {
    title: "Fire Fighting",
    description: "SEO, social media, and online marketing",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/digital-marketing",
  },
  {
    title: "Safety & Security",
    description: "Cloud infrastructure and deployment",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/cloud-solutions",
  },
  {
    title: "Energy Audit",
    description: "Technical consulting and strategy",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/consulting",
  },
  {
    title: "Solar",
    description: "Technical consulting and strategy",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/consulting",
  },
  {
    title: "Data Center",
    description: "Technical consulting and strategy",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/consulting",
  },
  {
    title: "IBMS",
    description: "Technical consulting and strategy",
    image: "/placeholder.svg?height=200&width=300",
    href: "/services/consulting",
  },
];

export default function Header() {
  const [hoveredService, setHoveredService] = useState(services[0]);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="border-b border-gray-400 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className=" border border-y-0 px-3 border-gray-400 h-full flex justify-center items-center ">
            <Link href="/" className="flex items-center space-x-2 ">
              <JsConsultants className="w-9 h-9" />
              <span className="text-xl font-semibold text-gray-800 font-space-mono">
                Consultants
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent hover:to-blue-primary data-[state=open]:bg-blue-200">
                    <Grid3X3 className="w-4 h-4 mr-2" />
                    Services
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="w-full max-w-4xl p-6">
                      <div className="flex gap-6 w-[800px]">
                        {/* Left side - Image */}
                        <div className="flex-shrink-0 w-1/3">
                          <div className="bg-gray-100 rounded-lg overflow-hidden">
                            <Image
                              src={hoveredService.image || "/placeholder.svg"}
                              alt={hoveredService.title}
                              width={320}
                              height={200}
                              className="w-full h-48 object-cover"
                            />
                            <div className="p-4">
                              <h3 className="font-semibold text-lg text-gray-900">
                                {hoveredService.title}
                              </h3>
                              <p className="text-gray-600 text-sm mt-2 leading-relaxed">
                                {hoveredService.description}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Right side - Menu items */}
                        <div className="flex-1 min-w-0">
                          <div className="grid grid-cols-2 gap-3">
                            {services.map((service, index) => (
                              <NavigationMenuLink
                                key={index}
                                asChild
                                onMouseEnter={() => setHoveredService(service)}
                              >
                                <Link
                                  href={service.href}
                                  className="block p-4 rounded-lg hover:bg-blue-50 transition-colors border border-transparent hover:border-blue-200 group"
                                >
                                  <div className="font-medium text-gray-900 group-hover:text-blue-700 transition-colors">
                                    {service.title}
                                  </div>
                                  <div className="text-sm text-gray-600 mt-1 line-clamp-2">
                                    {service.description}
                                  </div>
                                </Link>
                              </NavigationMenuLink>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <Link
              href="#"
              className="text-gray-700 hover:text-blue-900 font-medium"
            >
              Home
            </Link>
            <Link
              href="#"
              className="text-gray-700 hover:text-blue-900 font-medium"
            >
              About us
            </Link>
            <Link
              href="#"
              className="text-gray-700 hover:text-blue-900 font-medium"
            >
              Projects
            </Link>
            <Link
              href="#"
              className=" font-medium text-gray-700 hover:text-blue-900"
            >
              Contact us
            </Link>
          </div>

          {/* Enquire Now Button - Desktop */}
          <div className="hidden lg:flex border border-y-0 border-gray-400 h-full justify-center items-center p-4">
            <button className=" text-black">Enquire now</button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="lg:hidden">
              <button>
                <Menu className="h-6 w-6" />
                <span className="sr-only">Open menu</span>
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-8">
                <SheetClose asChild>
                  <Link
                    href="/"
                    className="text-lg font-medium hover:text-blue-600 transition-colors"
                  >
                    Home
                  </Link>
                </SheetClose>

                <div className="space-y-2">
                  <div className="text-lg font-medium text-gray-900 flex items-center">
                    <Grid3X3 className="w-4 h-4 mr-2" />
                    Services
                  </div>
                  <div className="pl-6 space-y-2">
                    {services.map((service, index) => (
                      <SheetClose key={index} asChild>
                        <Link
                          href={service.href}
                          className="block text-gray-600 hover:text-blue-600 transition-colors"
                        >
                          {service.title}
                        </Link>
                      </SheetClose>
                    ))}
                  </div>
                </div>

                <SheetClose asChild>
                  <Link
                    href="/about"
                    className="text-lg font-medium hover:text-blue-600 transition-colors"
                  >
                    About us
                  </Link>
                </SheetClose>

                <SheetClose asChild>
                  <Link
                    href="/projects"
                    className="text-lg font-medium hover:text-blue-600 transition-colors"
                  >
                    Projects
                  </Link>
                </SheetClose>

                <SheetClose asChild>
                  <Link
                    href="/contact"
                    className="text-lg font-medium hover:text-blue-600 transition-colors"
                  >
                    Contact us
                  </Link>
                </SheetClose>

                <div className="pt-4 border border-y-0 border-gray-950 h-full">
                  <SheetClose asChild>
                    <button className="w-full text-white">Enquire now</button>
                  </SheetClose>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}
